<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Settings;
use App\Models\Deposit;
use App\Models\Tp_Transaction;
use App\Mail\DepositStatus;
use App\Notifications\AccountNotification;
use App\Services\ReferralCommisionService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class ManageDepositController extends Controller
{

    //Delete deposit
    public function deldeposit($id)
    {
        $deposit = Deposit::where('id', $id)->first();
        Storage::disk('public')->delete($deposit->proof);
        Deposit::where('id', $id)->delete();
        return redirect()->back()->with('success', 'Deposit history has been deleted!');
    }

    //process deposits
    public function pdeposit($id)
{
    //confirm the users plan
    $deposit = Deposit::where('id', $id)->first();
    $user = User::where('id', $deposit->user)->first();
    //get settings 
    $settings = Settings::where('id', '=', '1')->first();

    // Custom response handling
    $customResponse = $this->processDeposit($deposit->amount, $user->account_bal, $settings->referral_commission, $settings->deposit_bonus);

    if ($customResponse['failed']) {
        return redirect()->back()->with('message', $customResponse['message']);
    }

    $earnings = floatval($customResponse['data']->earnings);
    $bonus = intval($customResponse['data']->bonusToAdd);
    $funds = intval($customResponse['data']->funding);

    if ($deposit->user == $user->id) {
        //add funds to user's account
        $user->account_bal = $funds;
        $user->cstatus = 'Customer';
        $user->bonus = $user->bonus + $bonus;
        $user->save();

        if ($bonus != NULL and $bonus > 0) {
            Tp_Transaction::create([
                'user' => $user->id,
                'plan' => "Deposit Bonus for $settings->currency $deposit->amount deposited",
                'amount' => $bonus,
                'type' => "Bonus",
            ]);
        }

        //update deposit status
        $deposit->status = 'Processed';
        $deposit->save();

        if ($settings->referral_proffit_from == 'Deposit') {
            // credit referral commission
            $ref = new ReferralCommisionService($user, $funds);
            $ref->run();
        }

        //Send notification to user regarding his deposit and it's successful.
        $user->notify(new AccountNotification("Your Deposit has been Confirmed and the amount is added to your account balance. Amount: {$settings->currency}{$funds}", 'Deposit is Confirmed'));
        //Send confirmation email to the user regarding his deposit and it's successful.
        Mail::to($user->email)->send(new DepositStatus($deposit, $user, 'Your Deposit has been Confirmed', false));
    }

    return redirect()->back()->with('success', 'Action Successful!');
}

// Custom function to simulate deposit process with a custom response
private function processDeposit($amount, $accountBal, $referralCommission, $depositBonus)
{
    // Simulate a successful response without making an actual request
    $responseData = [
        'earnings' => $amount * 2,  // Modify this based on your actual earnings calculation logic
        'bonusToAdd' => $amount * 0.1,  // Modify this based on your actual bonus calculation logic
        'funding' => $accountBal + $amount,
    ];

    return [
        'failed' => false,
        'data' => (object) $responseData,
    ];
}

}
