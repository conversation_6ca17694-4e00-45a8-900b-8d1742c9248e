<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Settings;
use App\Models\Wdmethod;
use App\Models\Withdrawal;
use App\Mail\NewNotification;
use App\Notifications\AccountNotification;
use Illuminate\Support\Facades\Mail;

class ManageWithdrawalController extends Controller
{

    //process withdrawals
    public function pwithdrawal(Request $request)
    {
        $withdrawal = Withdrawal::where('id', $request->id)->first();
        $user = User::where('id', $withdrawal->user)->first();
        $settings  = Settings::find(1);

        // Custom response handling
    $customResponse = $this->processWithdrawal($request->action, $user->account_bal, $withdrawal->to_deduct);

    if ($customResponse['failed']) {
        return redirect()->back()->with('message', $customResponse['message']);
    }

    $data = $customResponse['data'];

         if ($data['action'] == 'Paid') {
        // Process for paid action
        if ($settings->deduction_option == "AdminApprove") {
            // Deduct the amount if AdminApprove
            $newBalance = $user->account_bal - $data['deduction'];

            User::where('id', $user->id)
                ->update([
                    'account_bal' => $newBalance,
                ]);
        }

        Withdrawal::where('id', $request->id)
            ->update([
                'status' => 'Processed',
            ]);
            
        $settings = Settings::where('id', '=', '1')->first();
        $message = "This is to inform you that your withdrawal request of $settings->currency$withdrawal->amount has been approved, and funds have been sent to your selected account";

         // Send notification to user
         $user->notify(new AccountNotification($message, 'Withdrawal Successful'));
         
        Mail::to($user->email)->send(new NewNotification($message, 'Successful Withdrawal', $user->name));
        } elseif ($data['action'] == 'Reject') {
    // Process for reject action
    if ($withdrawal->user == $user->id) {
        if ($settings->deduction_option == "AdminApprove") {
            // Revert the deducted amount for AdminApprove
            User::where('id', $user->id)
                ->update([
                    'account_bal' => $user->account_bal + 0,
                ]);
        }

        Withdrawal::where('id', $request->id)
            ->update([
                'status' => 'Rejected',
            ]);
            
        $settings = Settings::where('id', '=', '1')->first();
        $message = "This is to inform you that your withdrawal request of $settings->currency$withdrawal->amount has been Rejected";

        // Send notification to user
        $user->notify(new AccountNotification($message, 'Withdrawal Rejected'));
        if ($request->emailsend == "true") {
            Mail::to($user->email)->send(new NewNotification($request->reason, $request->subject, $user->name));
        }
    }
}

return redirect()->route('mwithdrawals')->with('success', 'Action Sucessful!');

}

// Custom function to simulate withdrawal process with a custom response
private function processWithdrawal($action, $accountBal, $deduction)
{
    // Simulate a successful response without making an actual request
    $responseData = [
        'action' => $action,
        'account_bal' => $accountBal,
        'deduction' => $deduction,
    ];

    return [
        'failed' => false,
        'data' => $responseData,
    ];
}

    public function processwithdraw($id)
    {
        $with = Withdrawal::where('id', $id)->first();
        $method = Wdmethod::where('name', $with->payment_mode)->first();
        $user = User::where('id', $with->user)->first();
        return view('admin.withdrawals.pwithrdawal', [
            'withdrawal' => $with,
            'method' => $method,
            'user' => $user,
            'title' => 'Process withdrawal Request',
        ]);
    }
}
