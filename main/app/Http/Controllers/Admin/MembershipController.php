<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MembershipController extends Controller
{
    public function showCourses()
    {
        $courses = Course::all();
        $categories = Category::all();

        return view('admin.memebership.courses', [
            'courses' => $courses,
            'title' => 'Courses',
            'categories' => $categories,
        ]);
    }

    public function addCourse(Request $request)
{
    $path = '';

    if ($request->hasFile('image')) {
        $path = $request->file('image')->store('uploads', 'public');
        $path = str_replace('public/', '', $path);
    } else {
        $path = $request->image_url;
    }

    $category = Category::where('name', $request->category)->first();

    $course = new Course();
    $course->title = $request->title;
    $course->amount = $request->amount;
    $course->image_url = $path;
    $course->paidCourses = !empty($request->amount);
    $course->category_id = $category->id;
    $course->desc = $request->desc;
    $course->save();

    return redirect()->back()->with('success', 'Course added successfully.');
}


public function updateCourse(Request $request)
{
    $course = Course::findOrFail($request->course_id);
    
    $path = $course->image_url;

    if ($request->hasFile('image')) {
        $path = $request->file('image')->store('uploads', 'public');
        // Update the path to reflect the new storage location
        $path = str_replace('public/', '', $path);
    } else {
        $path = $request->image_url;
    }

    $course->title = $request->title;
    $course->amount = $request->amount;
    $course->image_url = $path;
    $course->paidCourses = !empty($request->amount);
    $course->category_id = $request->category;
    $course->desc = $request->desc;
    $course->save();

    return redirect()->back()->with('success', 'Course updated successfully.');
}


public function deleteCourse($courseId)
{
    Course::findOrFail($courseId)->delete();

    return redirect()->back()->with('success', 'Course deleted successfully.');
}

public function showLessons(string $courseId)
{
    $course = Course::findOrFail($courseId);
    $lessons = $course->lessons;

    return view('admin.memebership.lessons', [
        'lessons' => $lessons,
        'course' => $course,
        'categoryId' => $course->category_id,
        'title' => 'Lessons'
    ]);
}

public function addLesson(Request $request)
{
    $path = '';

    if ($request->hasFile('image')) {
        $path = $request->file('image')->store('uploads', 'public');
        $path = str_replace('public/', '', $path);
    } else {
        $path = $request->image_url;
    }

    $lesson = new Lesson();
    $lesson->title = $request->title;
    $lesson->length = $request->length;
    $lesson->videolink = $request->videolink;
    $lesson->preview = $request->preview;
    $lesson->course_id = $request->course_id;
    $lesson->category_id = $request->category_id;
    $lesson->desc = $request->desc;
    $lesson->thumbnail = $path;
    $lesson->save();

    return redirect()->back()->with('success', 'Lesson added successfully.');
}

public function updateLesson(Request $request)
{
    $lesson = Lesson::findOrFail($request->lesson_id);

    $path = $lesson->thumbnail;

    if ($request->hasFile('image')) {
        $path = $request->file('image')->store('uploads', 'public');
        $path = str_replace('public/', '', $path);
    } else {
        $path = $request->image_url;
    }

    $lesson->title = $request->title;
    $lesson->length = $request->length;
    $lesson->videolink = $request->videolink;
    $lesson->preview = $request->preview;
    $lesson->course_id = $request->course_id;
    $lesson->category_id = $request->category;
    $lesson->desc = $request->desc;
    $lesson->thumbnail = $path;
    $lesson->save();

    return redirect()->back()->with('success', 'Lesson updated successfully.');
}

public function deleteLesson($lessonId, string $courseId)
{
    Lesson::findOrFail($lessonId)->delete();

    return redirect()->back()->with('success', 'Lesson deleted successfully.');
}

public function category()
{
    $categories = Category::all();

    return view('admin.memebership.category', [
        'categories' => $categories,
        'title' => 'Course Category'
    ]);
}


public function addCategory(Request $request)
{
    $category = new Category();
    $category->name = $request->category;
    $category->save();

    return redirect()->back()->with('success', 'Category added successfully.');
}

public function deleteCategory($id)
{
    Category::findOrFail($id)->delete();

    return redirect()->back()->with('success', 'Category deleted successfully.');
}

public function lessonWithoutCourse()
{
    $lessons = Lesson::whereNull('course_id')->get();
    $categories = Category::all();

    return view('admin.memebership.lessons-without', [
        'title' => 'Lessons without courses',
        'lessons' => $lessons,
        'categories' => $categories,
    ]);
}

}