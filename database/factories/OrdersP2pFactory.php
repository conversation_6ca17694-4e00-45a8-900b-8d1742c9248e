<?php

namespace Database\Factories;

use App\Models\OrdersP2p;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrdersP2pFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrdersP2p::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
