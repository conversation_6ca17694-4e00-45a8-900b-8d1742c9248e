<?php

namespace App\Http\Livewire\User;

use App\Models\Category;
use App\Models\Course;
use App\Models\Settings;
use Livewire\Component;
use Livewire\WithPagination;

class SystemCourses extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    public $lessons;
    public $category = null;

    public function render()
    {
        $settings = Settings::select('theme')->find(1);
        $categories = Category::all();
        $courses = Course::with('lessons')
            ->when($this->category, function ($query) {
                $query->whereHas('category', function ($q) {
                    $q->where('name', $this->category);
                });
            })
            ->paginate(10);

        return view("{$settings->theme}.livewire.user.system-courses", [
            'categories' => $categories,
            'courses' => $courses,
        ]);
    }

    public function changeCategory($categoryName)
    {
        $this->category = $categoryName === 'others' ? null : $categoryName;
    }
}