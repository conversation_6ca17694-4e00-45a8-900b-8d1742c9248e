<?php

namespace App\Http\Livewire\User\AccountSettings;

use App\Models\Settings;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithFileUploads;

class UpdateProfilePicture extends Component
{
    public $photo;
    public $photoPath;

    use WithFileUploads;

    public function mount()
    {
        $this->photoPath = auth()->user()->profile_photo_path;
    }
    public function render()
    {
        $settings = Settings::select('theme')->find(1);
        return view("{$settings->theme}.livewire.user.account-settings.update-profile-picture");
    }

    public function update()
    {
        Log::info("Your profile picture");
        $this->validate([
            'photo' => ['required', 'image', 'max:1024', 'mimes:jpg,jpeg,png'],
        ]);

        $user = User::find(auth()->user()->id);

$photoPath = $this->photo->store('photo', 'public');
$newPhotoPath = '/storage/photo/' . basename($photoPath);

// Construct full source and destination paths
$sourcePath = storage_path('app/public/photo/' . basename($photoPath));
$destinationPath = public_path($newPhotoPath);

rename($sourcePath, $destinationPath);

$user->profile_photo_path = $newPhotoPath;
$user->save();



        $this->photoPath = $user->profile_photo_path;
        $this->dispatchBrowserEvent('profile-updated', ['message' => 'Your profile picture is updated successfully.', 'type' => 'success']);
    }
}
