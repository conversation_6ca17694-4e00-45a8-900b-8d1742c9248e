<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Deposit;
use App\Models\Settings;
use App\Models\Tp_Transaction;
use App\Models\User;
use App\Traits\PingServer;
use App\Notifications\AccountNotification;
use Illuminate\Http\Request;

class TopupController extends Controller
{
    use PingServer;
    
    //top up route
  public function topup(Request $request){

    $user = User::where('id', $request->user_id)->first();
    $userDeposit = Deposit::where('user', $request->user_id)->first();
    $settings = Settings::where('id', '=', '1')->first();

    $userBalance = $user->account_bal;
    $userBonus = $user->bonus;
    $userRoi = $user->roi;
    $userRef = $user->ref_bonus;
    $userDepositAmount = $userDeposit ? $userDeposit->amount : 0;

    if ($request->t_type == "Credit") {
        if ($request->type == "Bonus") {
            User::where('id', $request->user_id)
                ->update([
                    'bonus' => $userBonus + $request->amount,
                    'account_bal' => $userBalance + $request->amount,
                ]);
        } elseif ($request->type == "Profit") {
            User::where('id', $request->user_id)
                ->update([
                    'roi' => $userRoi + $request->amount,
                    'account_bal' => $userBalance + $request->amount,
                ]);
        } elseif ($request->type == "Ref_Bonus") {
            User::where('id', $request->user_id)
                ->update([
                    'ref_bonus' => $userRef + $request->amount,
                    'account_bal' => $userBalance + $request->amount,
                ]);
        } elseif ($request->type == "balance") {
            User::where('id', $request->user_id)
                ->update([
                    'account_bal' => $userBalance + $request->amount,
                ]);
        } elseif ($request->type == "Deposit") {
            $newDeposit = new Deposit();
            $newDeposit->amount = $request->amount;
            $newDeposit->payment_mode = 'Express Deposit';
            $newDeposit->status = 'Processed';
            $newDeposit->plan = $request->user_pln;
            $newDeposit->user = $request->user_id;
            $newDeposit->save();

            User::where('id', $request->user_id)
                ->update([
                    'account_bal' => $userBalance + $request->amount,
                ]);
        }

        // Add history
        Tp_Transaction::create([
            'user' => $request->user_id,
            'plan' => "Credit",
            'amount' => $request->amount,
            'type' => $request->type,
        ]);

    } elseif ($request->t_type == "Debit") {
        if ($request->type == "Bonus") {
            User::where('id', $request->user_id)
                ->update([
                    'bonus' => $userBonus - $request->amount,
                    'account_bal' => $userBalance - $request->amount,
                ]);
        } elseif ($request->type == "Profit") {
            User::where('id', $request->user_id)
                ->update([
                    'roi' => $userRoi - $request->amount,
                    'account_bal' => $userBalance - $request->amount,
                ]);
        } elseif ($request->type == "Ref_Bonus") {
            User::where('id', $request->user_id)
                ->update([
                    'ref_bonus' => $userRef - $request->amount,
                    'account_bal' => $userBalance - $request->amount,
                ]);
        } elseif ($request->type == "balance") {
            User::where('id', $request->user_id)
                ->update([
                    'account_bal' => $userBalance - $request->amount,
                ]);
        }

        // Add history
        Tp_Transaction::create([
            'user' => $request->user_id,
            'plan' => "Credit reversal",
            'amount' => $request->amount,
            'type' => $request->type,
        ]);
    }

    // Send notification to user
    $currency = Settings::select('currency')->find(1);
    $user->notify(new AccountNotification("You have a new {$request->type} transaction. Amount: {$settings->currency}{$request->amount}.", 'System Topup'));

    return redirect()->back()->with('success', 'Action Successful!');
}

}