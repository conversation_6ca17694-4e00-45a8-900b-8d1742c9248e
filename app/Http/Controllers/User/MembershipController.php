<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\CourseUser;
use App\Models\Settings;
use App\Models\Tp_Transaction;
use App\Models\User;
use App\Notifications\AccountNotification;
use App\Traits\TemplateTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    use TemplateTrait;

    public function courses()
    {
        $settings = Settings::select('theme')->find(1);

        return view("{$settings->theme}.user.membership.courses", [
            'title' => 'Courses',
            'courses' => Course::with('lessons')->get(),
        ]);
    }

    public function courseDetails(string $course, string $id)
{
    $settings = Settings::select('theme')->find(1);

    // Assuming you have a relationship to fetch lessons and category
    $course = Course::with('lessons', 'category')->findOrFail($id);

    // Fetching users who purchased the course
    $usersWhoPurchased = $course->users()->select('users.id')->pluck('id')->toArray();

    return view("{$settings->theme}.user.membership.courseDetails", [
        'title' => 'Course Details',
        'course' => $course,
        'lessons' => $course->lessons,
        'whoPurchased' => $usersWhoPurchased,
    ]);
}


public function myCoursesDetails($courseId)
{
    $settings = Settings::select('theme')->find(1);
    $course = Course::with('lessons')->findOrFail($courseId);

    // Retrieve the users who purchased the course along with the purchase date
    $usersWhoPurchased = CourseUser::where('course_id', $courseId)
                                    ->with('user')
                                    ->get();

    return view("{$settings->theme}.user.membership.mycourse-details", [
        'title' => 'Course Details',
        'course' => $course,
        'lessons' => $course->lessons,
        'whoPurchased' => $usersWhoPurchased,
    ]);
}


    public function myCourses()
    {
        $settings = Settings::select('theme')->find(1);
        $user = Auth::user();
        $courses = $user->courses;

        return view("{$settings->theme}.user.membership.my-course", [
            'title' => 'My Courses',
            'courses' => $courses,
        ]);
    }

    public function learning($lessonId, $courseId = null)
    {
        $settings = Settings::select('theme')->find(1);
        $lesson = Lesson::findOrFail($lessonId);
        $course = $courseId ? Course::with('lessons')->findOrFail($courseId) : null;

        return view("{$settings->theme}.user.membership.watchlesson", [
            'course' => $course,
            'lesson' => $lesson,
            'title' => 'Watch Lesson',
            'next' => $lesson->nextLesson(),
            'previous' => $lesson->previousLesson(),
        ]);
    }

    public function buyCourse(Request $request)
    {
        $user = User::find(Auth::user()->id);
        $course = Course::findOrFail($request->course);

        $amount = $course->amount ?? 0;

        if ($user->courses->contains($course)) {
            return redirect()->back()->with('message', 'You have already purchased this course. You can view it on the My Courses page.');
        }

        if ($user->account_bal < $amount) {
            return redirect()->back()->with('message', 'You have insufficient funds in your account balance to make this purchase. Please make a deposit.');
        }

        $user->account_bal = $user->account_bal - $amount;
        $user->courses()->attach($course->id);
        $user->save();

        $user->notify(new AccountNotification("Your course purchase is successful.", 'Purchase Course'));

        //create history
        Tp_Transaction::create([
            'user' => $user->id,
            'plan' => "Purchase Course",
            'amount' => $amount,
            'type' => "Education",
        ]);

        return redirect()->back()->with('success', 'Course purchased successfully.');
    }
}