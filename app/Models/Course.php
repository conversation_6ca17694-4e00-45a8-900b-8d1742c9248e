<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    protected $fillable = [
        'title', 'amount', 'image_url', 'paidCourses', 'category_id', 'desc'
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }
    

    public function users()
    {
    return $this->belongsToMany(User::class)->withTimestamps();
    }
}
