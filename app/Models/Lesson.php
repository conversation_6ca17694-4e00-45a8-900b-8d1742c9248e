<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Lesson extends Model
{
    protected $fillable = [
        'title', 'length', 'videolink', 'preview', 'course_id', 'category_id', 'desc', 'thumbnail'
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }
    public function nextLesson()
    {
        return static::where('course_id', $this->course_id)
            ->where('id', '>', $this->id)
            ->orderBy('id', 'asc')
            ->first();
    }

    public function previousLesson()
    {
        return static::where('course_id', $this->course_id)
            ->where('id', '<', $this->id)
            ->orderBy('id', 'desc')
            ->first();
    }
}
