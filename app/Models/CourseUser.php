<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CourseUser extends Model
{
    protected $table = 'course_user'; // Specify the name of the pivot table if it's different from the default convention

    protected $fillable = ['user_id', 'course_id', 'created_at']; // Fillable fields

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }
}
